"use client"
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { useSearchParams } from "next/navigation";
import Header from "@/components/home/<USER>";
import { FlightDetailPage } from "@/components/bookingDetail";
import { fetchFlightDetail } from "@/redux/slices/flightDetail";

export default function FlightDetailsPage() {
  const dispatch = useDispatch();
  const searchParams = useSearchParams();
  const { detail: flightDetail, loading, error } = useSelector((state) => state.flightDetail);

  // Get fareId from URL params or use hardcoded value
  // You can now access this page with: /flight-details?fareId=your-fare-id
  const fareId = searchParams.get('fareId') || searchParams.get('id') || 'fare-0001';

  useEffect(() => {
    // Fetch flight details when component mounts or fareId changes
    if (fareId) {
      dispatch(fetchFlightDetail(fareId));
    }
  }, [dispatch, fareId]);

  const breadcrumbItems = [
    { label: 'Flight Details', href: '/flight-details' }
  ];

  // Default flight data (fallback when API data is not available)
  const defaultFlightData = {
    image: "/assets/img/flight/01.jpg",
    route: "New York - Los Angeles",
    type: "One Way",
    rating: 4.5,
    reviews: 35,
    takeOffTime: "Sat, 25 Oct | 07:30AM",
    takeOffCode: "JFK",
    landingTime: "Sat, 25 Oct | 09:25AM",
    landingCode: "LAX",
    stops: "1 Stop (STN)",
    stopDuration: "1h 25m",
    airline: "Delta Airlines",
    flightType: "Economy",
    fareType: "Refundable",
    cancellationFee: "$50 / Per Person",
    flightChange: "$50 / Per Person",
    seatsSequence: "$50 Extra Charge",
    inflightFeatures: "Available",
    taxesFees: "$50"
  };

  // Map API data to component format (adjust based on actual API response structure)
  const flightData = flightDetail ? {
    ...defaultFlightData,
    // Map API response fields to component props
    // Adjust these mappings based on your actual API response structure
    route: flightDetail.route || defaultFlightData.route,
    airline: flightDetail.airline || defaultFlightData.airline,
    takeOffTime: flightDetail.departureTime || defaultFlightData.takeOffTime,
    landingTime: flightDetail.arrivalTime || defaultFlightData.landingTime,
    takeOffCode: flightDetail.departureCode || defaultFlightData.takeOffCode,
    landingCode: flightDetail.arrivalCode || defaultFlightData.landingCode,
    stops: flightDetail.stops || defaultFlightData.stops,
    flightType: flightDetail.class || defaultFlightData.flightType,
    price: flightDetail.price || defaultFlightData.price,
    // Add more mappings as needed based on your API response
  } : defaultFlightData;

  const aboutData = {
    title: `About ${flightData.airline}`,
    content: [
      "There are many variations of passengers of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text.",
      "There are many variations of passengers of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text."
    ]
  };

  // Show loading state
  if (loading) {
    return (
      <>
        <Header
          backgroundImage="/assets/img/breadcrumb/01.jpg"
          height="min-h-[50vh]"
          heroTitle="Flight Details"
          heroSubtitle="Loading flight information..."
          showBooking={false}
          breadcrumbItems={breadcrumbItems}
          className="-mt-50"
        />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading flight details...</p>
            </div>
          </div>
        </div>
      </>
    );
  }

  // Show error state
  if (error) {
    return (
      <>
        <Header
          backgroundImage="/assets/img/breadcrumb/01.jpg"
          height="min-h-[50vh]"
          heroTitle="Flight Details"
          heroSubtitle="Error loading flight information"
          showBooking={false}
          breadcrumbItems={breadcrumbItems}
          className="-mt-50"
        />
        <div className="container mx-auto px-4 py-8">
          <div className="flex justify-center items-center min-h-[400px]">
            <div className="text-center">
              <div className="text-red-500 text-6xl mb-4">⚠️</div>
              <h3 className="text-xl font-semibold text-gray-800 mb-2">Error Loading Flight Details</h3>
              <p className="text-gray-600 mb-4">{error}</p>
              <button
                onClick={() => dispatch(fetchFlightDetail(fareId))}
                className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Try Again
              </button>
            </div>
          </div>
        </div>
      </>
    );
  }

  return (
    <>
      <Header
        backgroundImage="/assets/img/breadcrumb/01.jpg"
        height="min-h-[50vh]"
        heroTitle="Flight Details"
        heroSubtitle="Complete information about your selected flight"
        showBooking={false}
        breadcrumbItems={breadcrumbItems}
        className="-mt-50"
      />

      <FlightDetailPage
        flightData={flightData}
        aboutData={aboutData}
        fareId={fareId}
        isLoading={loading}
        apiData={flightDetail}
      />
    </>
  );
}
