"use client"
import Header from "@/components/home/<USER>";
import { FlightDetailPage } from "@/components/bookingDetail";

export default function FlightDetailsPage() {
  const breadcrumbItems = [
    { label: 'Flight Details', href: '/flight-details' }
  ];

  // Sample flight data
  const flightData = {
    image: "/assets/img/flight/01.jpg",
    route: "New York - Los Angeles",
    type: "One Way",
    rating: 4.5,
    reviews: 35,
    takeOffTime: "Sat, 25 Oct | 07:30AM",
    takeOffCode: "JFK",
    landingTime: "Sat, 25 Oct | 09:25AM",
    landingCode: "LAX",
    stops: "1 Stop (STN)",
    stopDuration: "1h 25m",
    airline: "Delta Airlines",
    flightType: "Economy",
    fareType: "Refundable",
    cancellationFee: "$50 / Per Person",
    flightChange: "$50 / Per Person",
    seatsSequence: "$50 Extra Charge",
    inflightFeatures: "Available",
    taxesFees: "$50"
  };

  const aboutData = {
    title: "About Delta Airlines",
    content: [
      "There are many variations of passengers of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text.",
      "There are many variations of passengers of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text."
    ]
  };

  return (
    <>
      <Header
        backgroundImage="/assets/img/breadcrumb/01.jpg"
        height="min-h-[50vh]"
        heroTitle="Flight Details"
        heroSubtitle="Complete information about your selected flight"
        showBooking={false}
        breadcrumbItems={breadcrumbItems}
        className="-mt-50"
      />

      <FlightDetailPage
        flightData={flightData}
        aboutData={aboutData}
      />
    </>
  );
}
