"use client"
import { useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import Header from "@/components/home/<USER>";
import { fetchFlightDetail } from "@/redux/slices/flightDetail";

export default function FlightApiTestPage() {
  const dispatch = useDispatch();
  const { detail: flightDetail, loading, error } = useSelector((state) => state.flightDetail);
  const [fareId, setFareId] = useState('fare-0001');

  const breadcrumbItems = [
    { label: 'Flight API Test', href: '/flight-api-test' }
  ];

  const handleFetchFlightDetail = () => {
    if (fareId.trim()) {
      dispatch(fetchFlightDetail(fareId.trim()));
    }
  };

  // Auto-fetch on component mount with default fareId
  useEffect(() => {
    handleFetchFlightDetail();
  }, []);

  return (
    <>
      <Header
        backgroundImage="/assets/img/breadcrumb/01.jpg"
        height="min-h-[50vh]"
        heroTitle="Flight API Test"
        heroSubtitle="Test the flight details API integration"
        showBooking={false}
        breadcrumbItems={breadcrumbItems}
        className="-mt-50"
      />

      <div className="container mx-auto px-4 py-8 mb-20">
        <div className="max-w-4xl mx-auto">
          
          {/* API Test Controls */}
          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">API Test Controls</h2>
            
            <div className="flex gap-4 items-end mb-4">
              <div className="flex-1">
                <label htmlFor="fareId" className="block text-sm font-medium text-gray-700 mb-2">
                  Fare ID
                </label>
                <input
                  type="text"
                  id="fareId"
                  value={fareId}
                  onChange={(e) => setFareId(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter fare ID (e.g., fare-0001)"
                />
              </div>
              <button
                onClick={handleFetchFlightDetail}
                disabled={loading || !fareId.trim()}
                className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors"
              >
                {loading ? 'Loading...' : 'Fetch Flight Details'}
              </button>
            </div>

            <div className="text-sm text-gray-600">
              <p><strong>API Endpoint:</strong> /api/v1/city?fareId={fareId}</p>
              <p><strong>Current Status:</strong> 
                <span className={`ml-2 px-2 py-1 rounded text-xs ${
                  loading ? 'bg-yellow-100 text-yellow-800' : 
                  error ? 'bg-red-100 text-red-800' : 
                  flightDetail ? 'bg-green-100 text-green-800' : 
                  'bg-gray-100 text-gray-800'
                }`}>
                  {loading ? 'Loading' : error ? 'Error' : flightDetail ? 'Success' : 'Ready'}
                </span>
              </p>
            </div>
          </div>

          {/* Loading State */}
          {loading && (
            <div className="bg-white rounded-lg shadow-md p-8 mb-8">
              <div className="flex justify-center items-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
                <span className="text-gray-600">Fetching flight details...</span>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <div className="flex">
                  <div className="text-red-400 text-xl mr-3">⚠️</div>
                  <div>
                    <h3 className="text-lg font-medium text-red-800 mb-2">API Error</h3>
                    <p className="text-red-700">{error}</p>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Success State - API Response */}
          {flightDetail && !loading && (
            <div className="bg-white rounded-lg shadow-md p-6 mb-8">
              <h3 className="text-xl font-bold text-gray-800 mb-4">API Response</h3>
              <div className="bg-gray-50 rounded-md p-4 overflow-auto">
                <pre className="text-sm text-gray-700">
                  {JSON.stringify(flightDetail, null, 2)}
                </pre>
              </div>
            </div>
          )}

          {/* Integration Status */}
          <div className="bg-white rounded-lg shadow-md p-6">
            <h3 className="text-xl font-bold text-gray-800 mb-4">Integration Status</h3>
            
            <div className="space-y-3">
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                <span className="text-gray-700">Redux store configured ✓</span>
              </div>
              
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                <span className="text-gray-700">API endpoint configured ✓</span>
              </div>
              
              <div className="flex items-center">
                <div className="w-4 h-4 bg-green-500 rounded-full mr-3"></div>
                <span className="text-gray-700">Async thunk created ✓</span>
              </div>
              
              <div className="flex items-center">
                <div className={`w-4 h-4 rounded-full mr-3 ${
                  flightDetail && !error ? 'bg-green-500' : 'bg-yellow-500'
                }`}></div>
                <span className="text-gray-700">
                  API integration {flightDetail && !error ? 'working' : 'ready for testing'} 
                  {flightDetail && !error ? '✓' : '⏳'}
                </span>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
              <h4 className="font-medium text-blue-800 mb-2">Next Steps:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Test with different fareId values</li>
                <li>• Integrate with flight details page</li>
                <li>• Add error handling and retry logic</li>
                <li>• Map API response to UI components</li>
              </ul>
            </div>
          </div>

        </div>
      </div>
    </>
  );
}
