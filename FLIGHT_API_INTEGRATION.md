# Flight Details API Integration

This document explains how the flight details API has been integrated with the hardcoded fareId "fare-0001".

## Overview

The flight details API integration allows fetching flight information from the backend using a fareId parameter. The system is designed to work with both hardcoded values (for testing) and dynamic values (from user interactions).

## API Endpoint

- **URL**: `/api/v1/city?fareId={fareId}`
- **Method**: GET
- **Parameter**: `fareId` - The unique identifier for the flight fare

## Integration Components

### 1. Redux Store Setup

**File**: `src/redux/slices/flightDetail.js`
- Contains the async thunk `fetchFlightDetail` for API calls
- Manages loading, success, and error states
- Stores the flight detail response in the Redux store

### 2. API Service

**File**: `src/api/flightDetail.js`
- Contains the `fetchFlightDetails` function
- Uses axios instance for HTTP requests
- Handles the API call to fetch flight details

### 3. Flight Details Page

**File**: `src/app/[locale]/flight-details/page.js`
- Updated to use Redux for state management
- Fetches flight details on component mount
- Supports fareId from URL parameters or uses hardcoded "fare-0001"
- Includes loading and error states
- Maps API response to component props

### 4. Flight List Integration

**File**: `src/components/flightList/FlightCard.js`
- Updated "Select" button to navigate to flight details
- Generates fareId from flight ID (e.g., "fare-0001", "fare-0002")
- Uses Next.js router for navigation

### 5. API Test Page

**File**: `src/app/[locale]/flight-api-test/page.js`
- Dedicated page for testing the API integration
- Allows manual input of fareId values
- Shows API response, loading states, and errors
- Useful for debugging and testing

## Usage Examples

### 1. Using Hardcoded fareId

The system defaults to "fare-0001" when no fareId is provided:

```javascript
// Automatically uses "fare-0001"
router.push('/flight-details');
```

### 2. Using Dynamic fareId

Pass fareId as a URL parameter:

```javascript
// Uses specific fareId
router.push('/flight-details?fareId=fare-0001');
```

### 3. From Flight List

When users click "Select" on a flight card:

```javascript
// Generates fareId from flight ID
const fareId = `fare-${id.toString().padStart(4, '0')}`;
router.push(`/flight-details?fareId=${fareId}`);
```

## Testing the Integration

### 1. API Test Page

Visit `/flight-api-test` to:
- Test different fareId values
- View API responses
- Check error handling
- Monitor loading states

### 2. Flight Details Page

Visit `/flight-details` or `/flight-details?fareId=fare-0001` to:
- See the integrated flight details page
- Test with different fareId values
- Verify data mapping from API to UI

### 3. Flight List Integration

Visit `/flight-list` and:
- Click "Select" on any flight card
- Verify navigation to flight details with correct fareId
- Test the end-to-end user flow

## API Response Mapping

The system maps API response fields to UI components. Current mappings include:

```javascript
const flightData = {
  route: flightDetail.route || defaultValue,
  airline: flightDetail.airline || defaultValue,
  takeOffTime: flightDetail.departureTime || defaultValue,
  landingTime: flightDetail.arrivalTime || defaultValue,
  // Add more mappings based on actual API response
};
```

## Error Handling

The integration includes comprehensive error handling:

1. **Loading States**: Shows spinner while fetching data
2. **Error States**: Displays error messages with retry option
3. **Fallback Data**: Uses default values when API data is unavailable
4. **Network Errors**: Handles timeout and connection issues

## Environment Setup

Make sure to set the API base URL in your environment variables:

```env
NEXT_PUBLIC_API_BASE_URL=your_api_base_url_here
```

## Next Steps

1. **Test with Real API**: Replace mock data with actual API responses
2. **Enhance Mapping**: Add more field mappings based on API structure
3. **Add Caching**: Implement caching for better performance
4. **Error Recovery**: Add more sophisticated error recovery mechanisms
5. **Loading Optimization**: Implement skeleton loading states

## Navigation Links

- **Flight API Test**: `/flight-api-test`
- **Flight Details**: `/flight-details?fareId=fare-0001`
- **Flight List**: `/flight-list`

The integration is now ready for testing and can be easily extended to work with your actual API endpoints and data structures.
